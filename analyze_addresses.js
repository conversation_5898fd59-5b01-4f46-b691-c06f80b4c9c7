const fs = require('fs');

// 读取日志文件
const logContent = fs.readFileSync('batch_fix_showAddr.log', 'utf8');

// 提取所有包含'dryRun mode, skip update'的行
const dryRunLines = logContent.split('\n').filter(line => 
    line.includes('dryRun mode, skip update')
);

console.log(`找到 ${dryRunLines.length} 行包含 'dryRun mode, skip update'`);

// 提取'new:'后面的内容
const newValues = dryRunLines.map(line => {
    const match = line.match(/new:(.+)$/);
    return match ? match[1].trim() : '';
}).filter(value => value !== '');

console.log(`提取到 ${newValues.length} 个 'new:' 值`);

// 定义判断是否为正常地址的函数
function isNormalAddress(address) {
    // 去除前后空格
    address = address.trim();
    
    // 如果为空，不是正常地址
    if (!address) return false;
    
    // 异常模式列表
    const abnormalPatterns = [
        /^undefined$/i,
        /^null$/i,
        /^-+$/,  // 只有破折号
        /^\*+$/,  // 只有星号
        /^\.+$/,  // 只有点号
        /^#+$/,   // 只有井号
        /^x+$/i,  // 只有x
        /^\?+$/,  // 只有问号
        /^0+$/,   // 只有0
        /^\s*$/, // 只有空格
        /^[^a-zA-Z0-9]*$/, // 不包含字母或数字
        /^\(.*\)$/,  // 完全被括号包围
        /^\[.*\]$/,  // 完全被方括号包围
        /^{.*}$/,    // 完全被花括号包围
        /^<.*>$/,    // 完全被尖括号包围
        /^".*"$/,    // 完全被引号包围
        /^'.*'$/,    // 完全被单引号包围
        /^V\/L\s/i,  // 以V/L开头（Vacant Land）
        /^Vacant\s/i, // 以Vacant开头
        /Acreage$/i,  // 以Acreage结尾
        /^Block\s/i,  // 以Block开头
        /^Lot\s/i,    // 以Lot开头（但不是地址中的Lot）
        /^#\s*\d+\s*$/,  // 只有单元号
        /^Upper\s*$/i,   // 只有Upper
        /^Lower\s*$/i,   // 只有Lower
        /^Main\s*$/i,    // 只有Main
        /^Basement\s*$/i, // 只有Basement
        /^Bsmt\s*$/i,    // 只有Bsmt
        /^\d+\s*-\s*\d+\s*$/, // 只有数字范围
        /^[A-Z]\d+\s*$/, // 只有字母数字组合（如W1, V15等）
        /Road\s*Road$/i,  // 重复的Road
        /Street\s*Street$/i, // 重复的Street
        /Avenue\s*Avenue$/i, // 重复的Avenue
        /Drive\s*Drive$/i,   // 重复的Drive
        /^[^0-9]*\s+[^0-9]*$/,  // 没有门牌号的地址
        /^\d+\s+\d+\s*$/,  // 只有数字
        /^[A-Z]{2,}\s*$/,  // 只有大写字母
        /^[a-z]{2,}\s*$/,  // 只有小写字母
        /^\d+[A-Z]+\s*$/,  // 数字+字母组合（如123ABC）
        /^[A-Z]+\d+\s*$/,  // 字母+数字组合（如ABC123）
        /^\w+\s+\w+\s+\w+\s+\w+\s+\w+\s+\w+/,  // 超过6个单词的可能不是地址
        /^[^a-zA-Z]*\d+[^a-zA-Z]*$/,  // 只包含数字和特殊字符
        /^\d+\s*[,\.]\s*\d+/,  // 数字,数字 或 数字.数字 格式
        /^[A-Z]\d+[A-Z]\d+/,   // 复杂的字母数字组合
        /^\d+\s*\/\s*\d+/,     // 分数格式
        /^[A-Z]{1,3}\d{1,4}$/,  // 简单的字母数字代码
        /^\d{1,4}[A-Z]{1,3}$/,  // 简单的数字字母代码
        /^[A-Z]\s*\d+\s*[A-Z]\s*\d+/,  // 分散的字母数字
        /^Week\s/i,            // 以Week开头
        /^Villa\s/i,           // 以Villa开头
        /^Unit\s*[A-Z]\d*\s*$/i, // 只有Unit标识
        /^Rm\s*\d*\s*$/i,      // 只有Room标识
        /^Apt\s*\d*\s*$/i,     // 只有Apartment标识
        /^Suite\s*\d*\s*$/i,   // 只有Suite标识
        /^Floor\s*\d*\s*$/i,   // 只有Floor标识
        /^Level\s*\d*\s*$/i,   // 只有Level标识
        /^\d+\s*[A-Z]\s*\d+\s*[A-Z]/,  // 复杂的字母数字混合
        /^[A-Z]+\s*[A-Z]+\s*[A-Z]+\s*$/,  // 只有大写字母单词
        /^\d+\s*[A-Z]+\s*\d+\s*[A-Z]+/,   // 数字字母交替
        /^[A-Z]\d+\s*[A-Z]\d+\s*[A-Z]\d+/, // 多个字母数字组合
        /^\d+\s*[A-Z]{2,}\s*\d+/,          // 数字+多字母+数字
        /^[A-Z]{2,}\s*\d+\s*[A-Z]{2,}/,    // 多字母+数字+多字母
        /^\d+\s*[A-Z]\s*\d+\s*$/,          // 数字+字母+数字
        /^[A-Z]\s*\d+\s*[A-Z]\s*$/,        // 字母+数字+字母
        /^\d+\s*[A-Z]{3,}/,                 // 数字+3个以上字母
        /^[A-Z]{3,}\s*\d+\s*$/,            // 3个以上字母+数字
        /^\d+\s*[A-Z]\s*\d+\s*[A-Z]\s*\d+/, // 数字字母数字字母数字
        /^[A-Z]\d+[A-Z]\d+[A-Z]/,          // 字母数字字母数字字母
        /^\d+[A-Z]+\d+[A-Z]+/,             // 数字字母数字字母
        /^[A-Z]+\d+[A-Z]+\d+/,             // 字母数字字母数字
        /^\d+\s*[A-Z]{2}\s*\d+\s*[A-Z]{2}/, // 数字+双字母+数字+双字母
        /^[A-Z]{2}\s*\d+\s*[A-Z]{2}\s*\d+/, // 双字母+数字+双字母+数字
        /^\d+\s*[A-Z]\s*\d+\s*[A-Z]\s*\d+\s*[A-Z]/, // 长的字母数字序列
        /^[A-Z]\s*\d+\s*[A-Z]\s*\d+\s*[A-Z]\s*\d+/, // 长的字母数字序列
        /^[A-Z]{4,}/,                       // 4个以上连续大写字母
        /^\d{5,}/,                          // 5个以上连续数字
        /^[A-Z]\d{4,}/,                     // 字母+4个以上数字
        /^\d{4,}[A-Z]/,                     // 4个以上数字+字母
        /^[A-Z]{2}\d{3,}/,                  // 双字母+3个以上数字
        /^\d{3,}[A-Z]{2}/,                  // 3个以上数字+双字母
        /^[A-Z]{3}\d{2,}/,                  // 三字母+2个以上数字
        /^\d{2,}[A-Z]{3}/,                  // 2个以上数字+三字母
        /^[A-Z]\d[A-Z]\d[A-Z]/,            // 字母数字字母数字字母
        /^\d[A-Z]\d[A-Z]\d/,               // 数字字母数字字母数字
        /^[A-Z]\d{2}[A-Z]\d{2}/,           // 字母+双数字+字母+双数字
        /^\d{2}[A-Z]\d{2}[A-Z]/,           // 双数字+字母+双数字+字母
        /^[A-Z]\d{3}[A-Z]/,                // 字母+三数字+字母
        /^\d{3}[A-Z]\d/,                   // 三数字+字母+数字
        /^[A-Z]\d[A-Z]{2}\d/,              // 字母+数字+双字母+数字
        /^\d[A-Z]{2}\d[A-Z]/,              // 数字+双字母+数字+字母
        /^[A-Z]{2}\d[A-Z]\d/,              // 双字母+数字+字母+数字
        /^\d[A-Z]\d{2}[A-Z]/,              // 数字+字母+双数字+字母
        /^[A-Z]\d[A-Z]\d{2}/,              // 字母+数字+字母+双数字
        /^\d{2}[A-Z]\d[A-Z]/,              // 双数字+字母+数字+字母
        /^[A-Z]{2}\d{2}[A-Z]/,             // 双字母+双数字+字母
        /^\d[A-Z]{2}\d{2}/,                // 数字+双字母+双数字
        /^[A-Z]\d{2}[A-Z]{2}/,             // 字母+双数字+双字母
        /^\d{2}[A-Z]{2}\d/,                // 双数字+双字母+数字
        /^[A-Z]{3}\d[A-Z]/,                // 三字母+数字+字母
        /^\d[A-Z]{3}\d/,                   // 数字+三字母+数字
        /^[A-Z]\d[A-Z]{3}/,                // 字母+数字+三字母
        /^\d{3}[A-Z]{2}/,                  // 三数字+双字母
        /^[A-Z]{2}\d{3}/,                  // 双字母+三数字
        /^\d[A-Z]\d[A-Z]{2}/,              // 数字+字母+数字+双字母
        /^[A-Z]{2}\d[A-Z]\d/,              // 双字母+数字+字母+数字
        /^\d{2}[A-Z]{3}/,                  // 双数字+三字母
        /^[A-Z]{3}\d{2}/,                  // 三字母+双数字
        /^\d[A-Z]{4}/,                     // 数字+四字母
        /^[A-Z]{4}\d/,                     // 四字母+数字
        /^\d{4}[A-Z]/,                     // 四数字+字母
        /^[A-Z]\d{4}/,                     // 字母+四数字
        /^[A-Z]{5}/,                       // 五字母
        /^\d{6}/,                          // 六数字
        /^[A-Z]\d{5}/,                     // 字母+五数字
        /^\d{5}[A-Z]/,                     // 五数字+字母
        /^[A-Z]{2}\d{4}/,                  // 双字母+四数字
        /^\d{4}[A-Z]{2}/,                  // 四数字+双字母
        /^[A-Z]{3}\d{3}/,                  // 三字母+三数字
        /^\d{3}[A-Z]{3}/,                  // 三数字+三字母
        /^[A-Z]{4}\d{2}/,                  // 四字母+双数字
        /^\d{2}[A-Z]{4}/,                  // 双数字+四字母
        /^[A-Z]{5}\d/,                     // 五字母+数字
        /^\d[A-Z]{5}/,                     // 数字+五字母
        /^[A-Z]{6}/,                       // 六字母
        /^\d{7}/,                          // 七数字
    ];
    
    // 检查是否匹配任何异常模式
    for (const pattern of abnormalPatterns) {
        if (pattern.test(address)) {
            return false;
        }
    }
    
    // 检查是否包含基本的地址元素（门牌号 + 街道名）
    // 正常地址应该包含数字（门牌号）和字母（街道名）
    const hasNumber = /\d/.test(address);
    const hasLetter = /[a-zA-Z]/.test(address);
    
    if (!hasNumber || !hasLetter) {
        return false;
    }
    
    // 检查是否有常见的地址关键词
    const addressKeywords = [
        'street', 'st', 'avenue', 'ave', 'road', 'rd', 'drive', 'dr', 
        'lane', 'ln', 'court', 'ct', 'place', 'pl', 'boulevard', 'blvd',
        'circle', 'cir', 'way', 'crescent', 'cres', 'terrace', 'ter',
        'square', 'sq', 'parkway', 'pkwy', 'trail', 'heights', 'hill',
        'grove', 'gate', 'close', 'walk', 'path', 'mews', 'gardens',
        'park', 'ridge', 'view', 'point', 'bay', 'cove', 'creek',
        'private', 'pvt', 'highway', 'hwy'
    ];
    
    const lowerAddress = address.toLowerCase();
    const hasAddressKeyword = addressKeywords.some(keyword => 
        lowerAddress.includes(keyword)
    );
    
    // 如果没有地址关键词，可能不是正常地址
    if (!hasAddressKeyword) {
        // 但是如果格式看起来像地址（数字 + 单词），可能还是地址
        const basicAddressPattern = /^\d+\s+[a-zA-Z]/;
        if (!basicAddressPattern.test(address)) {
            return false;
        }
    }
    
    return true;
}

// 分析所有地址
const abnormalAddresses = [];
const normalAddresses = [];

newValues.forEach(address => {
    if (isNormalAddress(address)) {
        normalAddresses.push(address);
    } else {
        abnormalAddresses.push(address);
    }
});

console.log(`\n=== 分析结果 ===`);
console.log(`正常地址: ${normalAddresses.length} 个`);
console.log(`异常地址: ${abnormalAddresses.length} 个`);

// 输出异常地址
console.log(`\n=== 异常地址列表 ===`);
abnormalAddresses.forEach((address, index) => {
    console.log(`${index + 1}. "${address}"`);
});

// 保存结果到文件
const result = {
    total: newValues.length,
    normal: normalAddresses.length,
    abnormal: abnormalAddresses.length,
    abnormalAddresses: abnormalAddresses,
    normalAddresses: normalAddresses.slice(0, 10) // 只保存前10个正常地址作为示例
};

fs.writeFileSync('address_analysis_result.json', JSON.stringify(result, null, 2));
console.log(`\n结果已保存到 address_analysis_result.json`);

#!/usr/bin/env python3
import re
import json

def is_normal_address(address):
    """判断是否为正常地址"""
    address = address.strip()
    
    if not address:
        return False
    
    # 异常模式列表
    abnormal_patterns = [
        r'^undefined$',
        r'^null$',
        r'^-+$',  # 只有破折号
        r'^\*+$',  # 只有星号
        r'^\.+$',  # 只有点号
        r'^#+$',   # 只有井号
        r'^x+$',   # 只有x
        r'^\?+$',  # 只有问号
        r'^0+$',   # 只有0
        r'^\s*$',  # 只有空格
        r'^[^a-zA-Z0-9]*$',  # 不包含字母或数字
        r'^\(.*\)$',  # 完全被括号包围
        r'^\[.*\]$',  # 完全被方括号包围
        r'^{.*}$',    # 完全被花括号包围
        r'^<.*>$',    # 完全被尖括号包围
        r'^".*"$',    # 完全被引号包围
        r"^'.*'$",    # 完全被单引号包围
        r'^V/L\s',    # 以V/L开头（Vacant Land）
        r'^Vacant\s', # 以Vacant开头
        r'Acreage$',  # 以Acreage结尾
        r'^Block\s',  # 以Block开头
        r'^Lot\s',    # 以Lot开头（但不是地址中的Lot）
        r'^#\s*\d+\s*$',  # 只有单元号
        r'^Upper\s*$',   # 只有Upper
        r'^Lower\s*$',   # 只有Lower
        r'^Main\s*$',    # 只有Main
        r'^Basement\s*$', # 只有Basement
        r'^Bsmt\s*$',    # 只有Bsmt
        r'^\d+\s*-\s*\d+\s*$', # 只有数字范围
        r'^[A-Z]\d+\s*$', # 只有字母数字组合（如W1, V15等）
        r'Road\s*Road$',  # 重复的Road
        r'Street\s*Street$', # 重复的Street
        r'Avenue\s*Avenue$', # 重复的Avenue
        r'Drive\s*Drive$',   # 重复的Drive
        r'^\d+\s+\d+\s*$',  # 只有数字
        r'^[A-Z]{2,}\s*$',  # 只有大写字母
        r'^[a-z]{2,}\s*$',  # 只有小写字母
        r'^\d+[A-Z]+\s*$',  # 数字+字母组合（如123ABC）
        r'^[A-Z]+\d+\s*$',  # 字母+数字组合（如ABC123）
        r'^[^a-zA-Z]*\d+[^a-zA-Z]*$',  # 只包含数字和特殊字符
        r'^\d+\s*[,\.]\s*\d+',  # 数字,数字 或 数字.数字 格式
        r'^[A-Z]\d+[A-Z]\d+',   # 复杂的字母数字组合
        r'^\d+\s*/\s*\d+',     # 分数格式
        r'^[A-Z]{1,3}\d{1,4}$',  # 简单的字母数字代码
        r'^\d{1,4}[A-Z]{1,3}$',  # 简单的数字字母代码
        r'^Week\s',            # 以Week开头
        r'^Villa\s',           # 以Villa开头
        r'^Unit\s*[A-Z]\d*\s*$', # 只有Unit标识
        r'^Rm\s*\d*\s*$',      # 只有Room标识
        r'^Apt\s*\d*\s*$',     # 只有Apartment标识
        r'^Suite\s*\d*\s*$',   # 只有Suite标识
        r'^Floor\s*\d*\s*$',   # 只有Floor标识
        r'^Level\s*\d*\s*$',   # 只有Level标识
        r'^[A-Z]{4,}',         # 4个以上连续大写字母
        r'^\d{5,}',            # 5个以上连续数字
        r'^[A-Z]\d{4,}',       # 字母+4个以上数字
        r'^\d{4,}[A-Z]',       # 4个以上数字+字母
        r'^[A-Z]{2}\d{3,}',    # 双字母+3个以上数字
        r'^\d{3,}[A-Z]{2}',    # 3个以上数字+双字母
    ]
    
    # 检查是否匹配任何异常模式
    for pattern in abnormal_patterns:
        if re.search(pattern, address, re.IGNORECASE):
            return False
    
    # 检查是否包含基本的地址元素（门牌号 + 街道名）
    has_number = bool(re.search(r'\d', address))
    has_letter = bool(re.search(r'[a-zA-Z]', address))
    
    if not has_number or not has_letter:
        return False
    
    # 检查是否有常见的地址关键词
    address_keywords = [
        'street', 'st', 'avenue', 'ave', 'road', 'rd', 'drive', 'dr', 
        'lane', 'ln', 'court', 'ct', 'place', 'pl', 'boulevard', 'blvd',
        'circle', 'cir', 'way', 'crescent', 'cres', 'terrace', 'ter',
        'square', 'sq', 'parkway', 'pkwy', 'trail', 'heights', 'hill',
        'grove', 'gate', 'close', 'walk', 'path', 'mews', 'gardens',
        'park', 'ridge', 'view', 'point', 'bay', 'cove', 'creek',
        'private', 'pvt', 'highway', 'hwy'
    ]
    
    lower_address = address.lower()
    has_address_keyword = any(keyword in lower_address for keyword in address_keywords)
    
    # 如果没有地址关键词，检查是否有基本地址格式
    if not has_address_keyword:
        basic_address_pattern = r'^\d+\s+[a-zA-Z]'
        if not re.search(basic_address_pattern, address):
            return False
    
    return True

def main():
    # 读取日志文件
    try:
        with open('batch_fix_showAddr.log', 'r', encoding='utf-8') as f:
            log_content = f.read()
    except Exception as e:
        print(f"读取文件错误: {e}")
        return
    
    # 提取所有包含'dryRun mode, skip update'的行
    dry_run_lines = [line for line in log_content.split('\n') if 'dryRun mode, skip update' in line]
    
    print(f"找到 {len(dry_run_lines)} 行包含 'dryRun mode, skip update'")
    
    # 提取'new:'后面的内容
    new_values = []
    for line in dry_run_lines:
        match = re.search(r'new:(.+)$', line)
        if match:
            value = match.group(1).strip()
            if value:
                new_values.append(value)
    
    print(f"提取到 {len(new_values)} 个 'new:' 值")
    
    # 分析所有地址
    abnormal_addresses = []
    normal_addresses = []
    
    for address in new_values:
        if is_normal_address(address):
            normal_addresses.append(address)
        else:
            abnormal_addresses.append(address)
    
    print(f"\n=== 分析结果 ===")
    print(f"正常地址: {len(normal_addresses)} 个")
    print(f"异常地址: {len(abnormal_addresses)} 个")
    
    # 输出异常地址（前50个）
    print(f"\n=== 异常地址列表（前50个）===")
    for i, address in enumerate(abnormal_addresses[:50], 1):
        print(f"{i}. \"{address}\"")
    
    if len(abnormal_addresses) > 50:
        print(f"... 还有 {len(abnormal_addresses) - 50} 个异常地址")
    
    # 保存结果到文件
    result = {
        'total': len(new_values),
        'normal': len(normal_addresses),
        'abnormal': len(abnormal_addresses),
        'abnormal_addresses': abnormal_addresses,
        'normal_addresses': normal_addresses[:10]  # 只保存前10个正常地址作为示例
    }
    
    with open('address_analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到 address_analysis_result.json")
    
    # 输出一些统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总地址数: {len(new_values)}")
    print(f"正常地址数: {len(normal_addresses)} ({len(normal_addresses)/len(new_values)*100:.1f}%)")
    print(f"异常地址数: {len(abnormal_addresses)} ({len(abnormal_addresses)/len(new_values)*100:.1f}%)")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
import json
import re
from collections import defaultdict

def categorize_abnormal_address(address):
    """对异常地址进行分类"""
    address = address.strip()
    
    categories = []
    
    # 1. 包含单元/楼层信息的地址
    if re.search(r'^(basement|bsmt|upper|lower|main|floor|level|unit|apt|suite|room|rm)\s', address, re.IGNORECASE):
        categories.append("包含单元/楼层信息")
    
    # 2. 包含N/A的地址
    if 'N/A' in address or 'n/a' in address:
        categories.append("包含N/A")
    
    # 3. 包含多个地址的组合
    if '/' in address and re.search(r'\d+/\d+', address):
        categories.append("多地址组合")
    
    # 4. 包含LOT的地址
    if re.search(r'^lot\s', address, re.IGNORECASE) or re.search(r'\slot\s', address, re.IGNORECASE):
        categories.append("包含LOT")
    
    # 5. 包含Block的地址
    if re.search(r'^block\s', address, re.IGNORECASE) or re.search(r'\sblock\s', address, re.IGNORECASE):
        categories.append("包含Block")
    
    # 6. 包含Area的地址
    if re.search(r'^area\s', address, re.IGNORECASE):
        categories.append("包含Area")
    
    # 7. 包含Coach/Retail等特殊用途的地址
    if re.search(r'^(coach|retail|store|office|warehouse|whse)\s', address, re.IGNORECASE):
        categories.append("特殊用途标识")
    
    # 8. 只有Highway的地址
    if re.search(r'^highway\s\d+$', address, re.IGNORECASE):
        categories.append("仅Highway编号")
    
    # 9. 包含undefined/null的地址
    if re.search(r'^(undefined|null)$', address, re.IGNORECASE):
        categories.append("undefined/null")
    
    # 10. 包含特殊字符或格式的地址
    if re.search(r'^[^a-zA-Z0-9\s]', address) or re.search(r'[^a-zA-Z0-9\s\-\.,/()]', address):
        categories.append("包含特殊字符")
    
    # 11. 纯数字+字母组合（如邮政编码格式）
    if re.search(r'^\d+[A-Z]+\s*$', address) or re.search(r'^[A-Z]+\d+\s*$', address):
        categories.append("代码格式")
    
    # 12. 包含PART的地址
    if re.search(r'^part\s', address, re.IGNORECASE):
        categories.append("包含PART")
    
    # 13. 包含方向信息的地址
    if re.search(r'^(east|west|north|south|middle)\s', address, re.IGNORECASE):
        categories.append("包含方向信息")
    
    # 14. 包含back/front等位置信息
    if re.search(r'^(back|front|side|rear)\s', address, re.IGNORECASE) or address.lower() == 'back, back':
        categories.append("包含位置信息")
    
    # 15. 长数字序列
    if re.search(r'^\d{5,}', address):
        categories.append("长数字序列")
    
    # 16. 包含湖泊、岛屿等自然地理名称
    if re.search(r'(lake|island|river|creek|bay|point|beach)', address, re.IGNORECASE):
        categories.append("自然地理名称")
    
    # 17. 包含LINE/CONCESSION等农村地址格式
    if re.search(r'(line|concession)', address, re.IGNORECASE):
        categories.append("农村地址格式")
    
    # 18. 其他未分类
    if not categories:
        categories.append("其他")
    
    return categories

def main():
    # 读取分析结果
    try:
        with open('address_analysis_result.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取文件错误: {e}")
        return
    
    abnormal_addresses = data['abnormal_addresses']
    
    print(f"开始分析 {len(abnormal_addresses)} 个异常地址...")
    
    # 分类统计
    category_counts = defaultdict(int)
    category_examples = defaultdict(list)
    
    for address in abnormal_addresses:
        categories = categorize_abnormal_address(address)
        for category in categories:
            category_counts[category] += 1
            if len(category_examples[category]) < 5:  # 每个类别保存最多5个例子
                category_examples[category].append(address)
    
    # 输出结果
    print(f"\n=== 异常地址分类统计 ===")
    print(f"总异常地址数: {len(abnormal_addresses)}")
    print()
    
    # 按数量排序
    sorted_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)
    
    for category, count in sorted_categories:
        percentage = (count / len(abnormal_addresses)) * 100
        print(f"{category}: {count} 个 ({percentage:.1f}%)")
        print("  示例:")
        for example in category_examples[category]:
            print(f"    - \"{example}\"")
        print()
    
    # 保存分类结果
    result = {
        'total_abnormal': len(abnormal_addresses),
        'categories': dict(category_counts),
        'category_examples': dict(category_examples),
        'sorted_categories': sorted_categories
    }
    
    with open('abnormal_address_categories.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print("分类结果已保存到 abnormal_address_categories.json")

if __name__ == "__main__":
    main()
